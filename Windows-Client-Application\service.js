const Service = require("node-windows").Service;
const path = require("path");
const fs = require("fs");

// Enhanced logging function with multiple fallback locations
function log(message, isError = false) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${isError ? "ERROR: " : ""}${message}\n`;
  console.log(`[SERVICE] ${message}`);

  // Try multiple log locations
  const logPaths = [
    path.join(__dirname, "service.log"),
    path.join(process.cwd(), "service.log"),
    path.join(process.env.TEMP || "C:\\temp", "service.log"),
    "C:\\service.log",
  ];

  let logged = false;
  for (const logPath of logPaths) {
    try {
      fs.appendFileSync(logPath, logMessage);
      logged = true;
      break;
    } catch (e) {
      // Continue to next path if this one fails
    }
  }

  if (!logged) {
    console.error(`[SERVICE] Failed to write to any log file: ${message}`);
  }
}

log("=== SERVICE SCRIPT STARTING ===");
log(`Process ID: ${process.pid}`);
log(`Node version: ${process.version}`);
log(`Current directory: ${__dirname}`);
log(`Working directory: ${process.cwd()}`);
log(`Command line arguments: ${process.argv.join(" ")}`);

// Check if required files exist
const agentScriptPath = path.join(__dirname, "agent.js");
const configPath = path.join(__dirname, "agent-config.json");

log(`Checking required files...`);
log(`Agent script path: ${agentScriptPath}`);
log(`Config file path: ${configPath}`);

if (!fs.existsSync(agentScriptPath)) {
  log(`CRITICAL: Agent script not found at ${agentScriptPath}`, true);
  process.exit(1);
} else {
  log(`Agent script found`);
}

if (!fs.existsSync(configPath)) {
  log(`WARNING: Config file not found at ${configPath}`, true);
  log(`Agent will use default configuration`);
} else {
  log(`Config file found`);
}

// Get the path to the bundled node.exe - try multiple possible locations
const possibleNodePaths = [
  path.join(__dirname, "node.exe"),
  path.join(process.cwd(), "node.exe"),
  path.join(__dirname, "..", "node.exe"),
  process.execPath, // Fallback to current Node.js executable
];

let nodeExePath = null;
for (const nodePath of possibleNodePaths) {
  if (fs.existsSync(nodePath)) {
    nodeExePath = nodePath;
    log(`Found node.exe at: ${nodeExePath}`);
    break;
  }
}

if (!nodeExePath) {
  log(`CRITICAL: node.exe not found in any expected location`, true);
  log(`Searched paths: ${possibleNodePaths.join(", ")}`, true);
  log(`Using fallback: current Node.js executable at ${process.execPath}`);
  nodeExePath = process.execPath;
}

const svc = new Service({
  name: "WindowsAgentService",
  description: "Background Windows Asset Agent",
  script: agentScriptPath,
  nodeOptions: ["--harmony", "--max_old_space_size=4096"],
  wait: 2,
  grow: 0.5,
  maxRetries: 40,
  // Specify the full path to the bundled node.exe
  nodePath: nodeExePath,
});

log(`Service configuration created:`);
log(`Service name: ${svc.name}`);
log(`Service description: ${svc.description}`);
log(`Script path: ${svc.script}`);
log(`Node path: ${svc.nodePath}`);
log(`Node options: ${svc.nodeOptions.join(" ")}`);

svc.on("install", () => {
  log("=== SERVICE INSTALL EVENT TRIGGERED ===");
  log("Service installed successfully, attempting to start...");
  svc.start();
  log("Service start command issued");
});

svc.on("uninstall", () => {
  log("=== SERVICE UNINSTALL EVENT TRIGGERED ===");
  log("Service uninstalled successfully");
});

svc.on("start", () => {
  log("=== SERVICE START EVENT TRIGGERED ===");
  log("Service started successfully");
  // Note: svc.pid may be undefined in some node-windows versions
  if (svc.pid) {
    log(`Service process ID: ${svc.pid}`);
  } else {
    log("Service process ID not available");
  }
  log("Service is now running");
});

svc.on("stop", () => {
  log("=== SERVICE STOP EVENT TRIGGERED ===");
  log("Service stopped");
  log("Service has stopped");
});

svc.on("error", (err) => {
  log(`=== SERVICE ERROR EVENT TRIGGERED ===`, true);
  log(`Service error: ${err.message}`, true);
  log(`Error stack: ${err.stack}`, true);
});

svc.on("alreadyinstalled", () => {
  log("=== SERVICE ALREADY INSTALLED ===");
  log("Service is already installed");
});

svc.on("invalidinstallation", () => {
  log("=== INVALID INSTALLATION ===", true);
  log("Service installation is invalid", true);
});

// Handle command line arguments
const command = process.argv[2];
log(`Processing command: ${command}`);

if (command === "install") {
  log("=== INSTALLING SERVICE ===");
  log("Calling service.install()...");
  svc.install();
} else if (command === "uninstall") {
  log("=== UNINSTALLING SERVICE ===");
  log("Calling service.uninstall()...");
  svc.uninstall();
} else if (command === "start") {
  log("=== STARTING SERVICE ===");
  log("Calling service.start()...");
  svc.start();
} else if (command === "stop") {
  log("=== STOPPING SERVICE ===");
  log("Calling service.stop()...");
  svc.stop();
} else if (command === "restart") {
  log("=== RESTARTING SERVICE ===");
  log("Calling service.restart()...");
  svc.restart();
} else {
  log("=== INVALID COMMAND ===", true);
  log("Usage: node service.js install|uninstall|start|stop|restart", true);
  log("Available commands:", true);
  log("  install   - Install and start the service", true);
  log("  uninstall - Stop and remove the service", true);
  log("  start     - Start the service", true);
  log("  stop      - Stop the service", true);
  log("  restart   - Restart the service", true);
  // Use exit code 0 to prevent NSIS installer from showing error dialog
  log("Exiting with code 0 to prevent installer error dialog");
  process.exit(0);
}

log("=== SERVICE SCRIPT COMPLETED ===");
