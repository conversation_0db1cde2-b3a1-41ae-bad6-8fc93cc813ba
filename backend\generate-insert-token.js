const crypto = require('crypto');
const mysql = require('mysql2/promise');

async function main() {
  const token = crypto.randomBytes(32).toString('hex');
  const description = 'Windows Agent Token';

  // Update these credentials if needed
  const db = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'shashank',
    database: 'windowsagent'
  });

  await db.query('INSERT INTO api_tokens (token, description) VALUES (?, ?)', [token, description]);
  await db.end();

  console.log('Generated API Token:', token);
}

main().catch(err => {
  console.error('Error:', err.message);
  process.exit(1);
}); 