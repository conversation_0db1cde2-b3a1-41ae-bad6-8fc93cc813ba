2025-08-20T17:38:06.651Z - === AGENT STARTING ===
2025-08-20T17:38:06.655Z - Process ID: 18784
2025-08-20T17:38:06.656Z - Node version: v22.17.1
2025-08-20T17:38:06.657Z - Current directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T17:38:06.658Z - Working directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T17:38:06.658Z - Environment: production
2025-08-20T17:38:06.659Z - Node executable path: E:\projects\Windows-Agent-App\Windows-Client-Application\node.exe
2025-08-20T17:38:06.660Z - Node executable directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T17:38:06.661Z - All required modules are accessible
2025-08-20T17:38:06.662Z - Looking for config at: E:\projects\Windows-Agent-App\Windows-Client-Application\agent-config.json
2025-08-20T17:38:06.664Z - Config loaded successfully from file
2025-08-20T17:38:06.664Z - API URL: http://localhost:5000/assets
2025-08-20T17:38:06.665Z - API Token: f3bf932d76...
2025-08-20T17:38:06.665Z - Interval: 24 hours
2025-08-20T17:38:06.666Z - === AGENT SERVICE STARTING ===
2025-08-20T17:38:06.667Z - Will send data every 24 hours
2025-08-20T17:38:06.668Z - Interval in milliseconds: 86400000
2025-08-20T17:38:06.668Z - Sending initial data...
2025-08-20T17:38:06.669Z - === STARTING DATA SEND ===
2025-08-20T17:38:06.670Z - Target API URL: http://localhost:5000/assets
2025-08-20T17:38:06.671Z - Using API token: f3bf932d76...
2025-08-20T17:38:06.671Z - Collecting system and software information...
2025-08-20T17:38:06.673Z - Starting system information collection...
2025-08-20T17:38:06.678Z - Starting software information collection...
2025-08-20T17:38:06.696Z - Setting up interval timer for 86400000ms (24 hours)
2025-08-20T17:38:06.697Z - Interval timer set with ID: 21
2025-08-20T17:38:06.698Z - === AGENT SERVICE STARTED SUCCESSFULLY ===
2025-08-20T17:38:12.762Z - System info collected successfully
2025-08-20T17:38:12.763Z - Hostname: LAPTOP-KMAUR1TO
2025-08-20T17:38:12.764Z - OS: Microsoft Windows 11 Home Single Language 10.0.26100
2025-08-20T17:38:12.765Z - Manufacturer: HP
2025-08-20T17:38:12.765Z - Model: HP Pavilion Gaming Laptop 15-ec2xxx
2025-08-20T17:38:12.766Z - CPU: AMD Ryzen 5 5600H with Radeon Graphics
2025-08-20T17:38:12.767Z - RAM: 7 GB
2025-08-20T17:38:12.767Z - Disks: 1 found
2025-08-20T17:38:12.768Z - Network interfaces: 7 found
2025-08-20T17:38:29.196Z - Software info collected: 98 applications found
2025-08-20T17:38:29.198Z - Sample applications: 20221020, 20250131, 20221020
2025-08-20T17:38:29.199Z - Payload prepared with 12 system fields and 98 software items
2025-08-20T17:38:29.200Z - Making HTTP POST request to API...
2025-08-20T17:38:29.279Z - ERROR: === DATA SEND FAILED after 22610ms ===
2025-08-20T17:38:29.280Z - ERROR: Error code: ECONNREFUSED
2025-08-20T17:38:29.280Z - ERROR: No response received from server
2025-08-20T17:38:29.284Z - ERROR: Unhandled rejection at: [object Promise]
2025-08-20T17:38:29.285Z - ERROR: Unhandled rejection reason: TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Agent'
    |     property 'sockets' -> object with constructor 'Object'
    |     property 'localhost:5000:' -> object with constructor 'Array'
    |     ...
    |     property '_httpMessage' -> object with constructor 'ClientRequest'
    --- property 'agent' closes the circle
