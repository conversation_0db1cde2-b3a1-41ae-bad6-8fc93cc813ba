import React, { useEffect, useState } from 'react';
import { fetchAssets, api } from '../api';
import { Box, Typography, Paper, TextField, Button, Alert } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useNavigate } from 'react-router-dom';

export default function AssetListPage() {
  const [assets, setAssets] = useState([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState({ hostname: '', ip: '', serial: '' });
  const [searching, setSearching] = useState(false);
  const navigate = useNavigate();

  const loadAssets = () => {
    setLoading(true);
    fetchAssets()
      .then(setAssets)
      .catch(e => setError(e.response?.data?.error || 'Failed to load assets'))
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    loadAssets();
    // eslint-disable-next-line
  }, []);

  const handleSearch = async (e) => {
    e.preventDefault();
    setSearching(true);
    setError('');
    try {
      const res = await api.get('/assets/search', { params: search });
      setAssets(res.data);
    } catch (e) {
      setError(e.response?.data?.error || 'Search failed');
    } finally {
      setSearching(false);
    }
  };

  const columns = [
    { field: 'hostname', headerName: 'Hostname', flex: 1 },
    { field: 'ip_addresses', headerName: 'IP Addresses', flex: 1, valueGetter: p => (JSON.parse(p.row.ip_addresses || '[]') || []).join(', ') },
    { field: 'os_name', headerName: 'OS', flex: 1 },
    { field: 'ram_total', headerName: 'RAM (GB)', flex: 0.5, valueGetter: p => Math.round((p.row.ram_total || 0) / (1024*1024*1024)) },
    { field: 'actions', headerName: 'Actions', flex: 0.5, renderCell: (params) => (
      <Button variant="outlined" size="small" onClick={() => navigate(`/assets/${params.row.id}`)}>Details</Button>
    ) }
  ];

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>Asset List</Typography>
      <Paper sx={{ p: 2, mb: 2 }}>
        <form onSubmit={handleSearch} style={{ display: 'flex', gap: 16, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField label="Hostname" value={search.hostname} onChange={e => setSearch(s => ({ ...s, hostname: e.target.value }))} size="small" />
          <TextField label="IP Address" value={search.ip} onChange={e => setSearch(s => ({ ...s, ip: e.target.value }))} size="small" />
          <TextField label="Serial" value={search.serial} onChange={e => setSearch(s => ({ ...s, serial: e.target.value }))} size="small" />
          <Button type="submit" variant="contained" disabled={searching}>Search</Button>
          <Button type="button" variant="outlined" onClick={loadAssets} disabled={loading}>Reset</Button>
        </form>
      </Paper>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      <Paper sx={{ height: 500 }}>
        <DataGrid
          rows={assets}
          columns={columns}
          getRowId={row => row.id}
          loading={loading}
          pageSize={10}
          rowsPerPageOptions={[10, 25, 50]}
        />
      </Paper>
    </Box>
  );
} 