import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { useAuth } from './AuthContext';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import AssetListPage from './pages/AssetListPage';
import AssetDetailPage from './pages/AssetDetailPage';
import SoftwareInventoryPage from './pages/SoftwareInventoryPage';
import HardwareSummaryPage from './pages/HardwareSummaryPage';
import SearchPage from './pages/SearchPage';
import ExportPage from './pages/ExportPage';
import UserManagementPage from './pages/UserManagementPage';
import ApiTokenPage from './pages/ApiTokenPage';
import NavBar from './components/NavBar';
import { Box } from '@mui/material';

function RequireAuth({ children }) {
  const { user } = useAuth();
  const location = useLocation();
  if (!user) return <Navigate to="/" state={{ from: location }} replace />;
  return children;
}

export default function App() {
  return (
    <Router>
      <CssBaseline />
      <NavBar />
      <Box sx={{ ml: { md: 25, xs: 0 }, mt: 2 }}>
        <Routes>
          <Route path="/" element={<LoginPage />} />
          <Route path="/dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
          <Route path="/assets" element={<RequireAuth><AssetListPage /></RequireAuth>} />
          <Route path="/assets/:id" element={<RequireAuth><AssetDetailPage /></RequireAuth>} />
          <Route path="/software" element={<RequireAuth><SoftwareInventoryPage /></RequireAuth>} />
          <Route path="/hardware" element={<RequireAuth><HardwareSummaryPage /></RequireAuth>} />
          <Route path="/search" element={<RequireAuth><SearchPage /></RequireAuth>} />
          <Route path="/export" element={<RequireAuth><ExportPage /></RequireAuth>} />
          <Route path="/users" element={<RequireAuth><UserManagementPage /></RequireAuth>} />
          <Route path="/tokens" element={<RequireAuth><ApiTokenPage /></RequireAuth>} />
        </Routes>
      </Box>
    </Router>
  );
} 