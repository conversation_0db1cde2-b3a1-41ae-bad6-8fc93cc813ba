import React, { useEffect, useState } from 'react';
import { fetchSummary } from '../api';
import { Box, Typography, Paper, Grid, List, ListItem, ListItemText, Alert } from '@mui/material';

export default function Dashboard() {
  const [summary, setSummary] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSummary()
      .then(setSummary)
      .catch(e => setError(e.response?.data?.error || 'Failed to load summary'))
      .finally(() => setLoading(false));
  }, []);

  if (loading) return <Typography>Loading dashboard...</Typography>;
  if (error) return <Alert severity="error">{error}</Alert>;
  if (!summary) return null;

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>Dashboard</Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Total Assets</Typography>
            <Typography variant="h3">{summary.byLocation.reduce((a, b) => a + b.count, 0)}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Assets by Location</Typography>
            <List>
              {summary.byLocation.map(loc => (
                <ListItem key={loc.location || 'Unknown'}>
                  <ListItemText primary={loc.location || 'Unknown'} secondary={loc.count + ' assets'} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={5}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Assets by OS</Typography>
            <List>
              {summary.byOs.map(os => (
                <ListItem key={os.os_name || 'Unknown'}>
                  <ListItemText primary={os.os_name || 'Unknown'} secondary={os.count + ' assets'} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Outdated Software</Typography>
            {summary.outdated.length === 0 ? (
              <Typography>No outdated software detected.</Typography>
            ) : (
              <List>
                {summary.outdated.map(sw => (
                  <ListItem key={sw.id + '-' + sw.asset_id}>
                    <ListItemText primary={sw.name + ' (' + sw.version + ')'} secondary={sw.hostname} />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Low RAM Alerts (&lt; 4GB)</Typography>
            {summary.lowRam.length === 0 ? (
              <Typography>No low RAM assets.</Typography>
            ) : (
              <List>
                {summary.lowRam.map(asset => (
                  <ListItem key={asset.hostname}>
                    <ListItemText primary={asset.hostname} secondary={Math.round(asset.ram_total / (1024*1024*1024)) + ' GB'} />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
} 