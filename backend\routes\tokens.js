const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const router = express.Router();

function checkJwt(req, res, next) {
  const auth = req.headers['authorization'] || '';
  const token = auth.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'Missing token' });
  try {
    req.user = jwt.verify(token, process.env.JWT_SECRET);
    if (req.user.role !== 'admin') return res.status(403).json({ error: 'Admin only' });
    next();
  } catch {
    return res.status(401).json({ error: 'Invalid token' });
  }
}

// GET /tokens - list tokens
router.get('/', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [tokens] = await db.query('SELECT id, token, description, created_at, last_used FROM api_tokens');
  res.json(tokens);
});

// POST /tokens - create token
router.post('/', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const { description } = req.body;
  const token = crypto.randomBytes(32).toString('hex');
  await db.query('INSERT INTO api_tokens (token, description) VALUES (?, ?)', [token, description || null]);
  res.json({ token });
});

// DELETE /tokens/:id - delete token
router.delete('/:id', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  await db.query('DELETE FROM api_tokens WHERE id = ?', [req.params.id]);
  res.json({ message: 'Token deleted' });
});

module.exports = router; 