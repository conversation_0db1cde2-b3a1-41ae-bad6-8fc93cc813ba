!macro customInstall
  MessageBox MB_OK "Custom install macro triggered at $INSTDIR"
  
  ; Log installation details
  DetailPrint "=== WINDOWS AGENT INSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"
  
  ; Note: File checks will be done in customInstallRun after files are extracted
  DetailPrint "Files will be verified after installation completes"
!macroend

!macro customInstallRun
  DetailPrint "=== POST-INSTALLATION VERIFICATION ==="
  DetailPrint "Installation directory: $INSTDIR"
  
  ; Check if node.exe exists (after files are extracted)
  DetailPrint "Checking for node.exe..."
  IfFileExists "$INSTDIR\node.exe" nodeExists 0
    DetailPrint "ERROR: node.exe not found at $INSTDIR"
    MessageBox MB_ICONSTOP "Critical Error: node.exe not found at $INSTDIR$\r$\n$\r$\nThis file is required for the Windows Agent service to function properly.$\r$\n$\r$\nPlease ensure the installation package is complete and try again."
    Abort
  nodeExists:
    DetailPrint "node.exe found at $INSTDIR"
  
  ; Check if service.js exists
  DetailPrint "Checking for service.js..."
  IfFileExists "$INSTDIR\service.js" serviceExists 0
    DetailPrint "ERROR: service.js not found at $INSTDIR"
    MessageBox MB_ICONSTOP "service.js not found at $INSTDIR"
    Abort
  serviceExists:
    DetailPrint "service.js found at $INSTDIR"

  ; Check if agent.js exists
  DetailPrint "Checking for agent.js..."
  IfFileExists "$INSTDIR\agent.js" agentExists 0
    DetailPrint "ERROR: agent.js not found at $INSTDIR"
    MessageBox MB_ICONSTOP "agent.js not found at $INSTDIR"
    Abort
  agentExists:
    DetailPrint "agent.js found at $INSTDIR"

  ; Check if agent-config.json exists
  DetailPrint "Checking for agent-config.json..."
  IfFileExists "$INSTDIR\agent-config.json" configExists 0
    DetailPrint "WARNING: agent-config.json not found, will use defaults"
  configExists:
    DetailPrint "agent-config.json check completed"
  
  ; All files found, proceed with service installation
  DetailPrint "=== INSTALLING WINDOWS SERVICE ==="
  DetailPrint "Using bundled node.exe for service installation"
  DetailPrint "Executing: $INSTDIR\node.exe $INSTDIR\service.js install"

  ; Execute the service installation using the bundled node.exe
  ExecWait '"$INSTDIR\node.exe" "$INSTDIR\service.js" install' $0
  
  DetailPrint "Service installation completed with exit code: $0"
  
  ; Check if service installation was successful
  IntCmp $0 0 installSuccess installFailed installFailed
  installSuccess:
    DetailPrint "Service installation successful"
    MessageBox MB_OK "Windows Agent service installed successfully!"
    Goto installEnd
  installFailed:
    DetailPrint "Service installation failed with exit code: $0"
    MessageBox MB_ICONEXCLAMATION "Service installation may have failed. Check service.log for details."
  
  installEnd:
    DetailPrint "=== WINDOWS AGENT INSTALLATION COMPLETED ==="
!macroend

!macro customUnInstall
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"
  
  ; Stop and uninstall the service
  DetailPrint "Stopping and uninstalling Windows Agent service..."
  ExecWait '"$INSTDIR\node.exe" "$INSTDIR\service.js" uninstall' $0

  DetailPrint "Service uninstallation completed with exit code: $0"

  ; Clean up log files
  DetailPrint "Cleaning up log files..."
  Delete "$INSTDIR\agent.log"
  Delete "$INSTDIR\service.log"
  Delete "$INSTDIR\agent-error.log"
  Delete "C:\agent.log"
  Delete "C:\service.log"
  
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION COMPLETED ==="
  MessageBox MB_OK "Custom uninstall macro triggered at $INSTDIR"
!macroend

Section "Install"
  DetailPrint "=== MAIN INSTALL SECTION RUNNING ==="
  MessageBox MB_OK "Main Install Section Running"
  !insertmacro customInstall
SectionEnd
