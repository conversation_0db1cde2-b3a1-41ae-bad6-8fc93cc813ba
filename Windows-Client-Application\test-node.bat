@echo off
echo === TESTING BUNDLED NODE.EXE ===
echo.

REM Check if we're in the right directory
if not exist "node.exe" (
    echo ERROR: node.exe not found in current directory
    echo Please run this script from the Windows-Client-Application directory
    pause
    exit /b 1
)

echo Found node.exe in current directory
echo.

REM Test node.exe version
echo Testing node.exe version:
node.exe --version
if %errorlevel% neq 0 (
    echo ERROR: Failed to get node.exe version
    pause
    exit /b 1
)
echo.

REM Test if node.exe can run the test script
echo Testing node.exe with test script:
node.exe test-node.js
if %errorlevel% neq 0 (
    echo ERROR: Failed to run test script with bundled node.exe
    pause
    exit /b 1
)
echo.

REM Test if node.exe can run the agent script
echo Testing node.exe with agent script (will exit immediately):
node.exe agent.js
echo Agent script test completed
echo.

echo === ALL TESTS COMPLETED ===
echo If all tests passed, the bundled node.exe should work correctly
pause 