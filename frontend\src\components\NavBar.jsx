import React from 'react';
import { AppB<PERSON>, Toolbar, <PERSON>po<PERSON>, Button, Drawer, List, ListItem, ListItemText, Box } from '@mui/material';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../AuthContext';

const navLinks = [
  { label: 'Dashboard', path: '/dashboard' },
  { label: 'Assets', path: '/assets' },
  { label: 'Export', path: '/export' },
  { label: 'Users', path: '/users' },
  { label: 'API Tokens', path: '/tokens' },
];

export default function NavBar() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>Asset Management</Typography>
          {user && (
            <>
              <Typography sx={{ mr: 2 }}>{user.username} ({user.role})</Typography>
              <Button color="inherit" onClick={handleLogout}>Logout</Button>
            </>
          )}
        </Toolbar>
      </AppBar>
      {user && (
        <Drawer variant="permanent" anchor="left" sx={{ width: 200, flexShrink: 0, '& .MuiDrawer-paper': { width: 200, boxSizing: 'border-box' } }}>
          <Toolbar />
          <Box sx={{ overflow: 'auto' }}>
            <List>
              {navLinks.map(link => (
                <ListItem button key={link.path} component={Link} to={link.path} selected={location.pathname.startsWith(link.path)}>
                  <ListItemText primary={link.label} />
                </ListItem>
              ))}
            </List>
          </Box>
        </Drawer>
      )}
    </>
  );
} 