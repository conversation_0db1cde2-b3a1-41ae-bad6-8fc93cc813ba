const fs = require('fs');
const path = require('path');

console.log('=== NODE.EXE TEST SCRIPT ===');
console.log(`Node version: ${process.version}`);
console.log(`Current directory: ${__dirname}`);
console.log(`Working directory: ${process.cwd()}`);

// Test if we can read files
const testFiles = [
  'agent.js',
  'service.js',
  'agent-config.json',
  'node.exe'
];

console.log('\n=== TESTING FILE ACCESS ===');
testFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✓ ${file} exists (${stats.size} bytes)`);
  } else {
    console.log(`✗ ${file} NOT FOUND`);
  }
});

// Test if we can require modules
console.log('\n=== TESTING MODULE REQUIREMENTS ===');
try {
  const axios = require('axios');
  console.log('✓ axios module loaded successfully');
} catch (e) {
  console.log(`✗ Failed to load axios: ${e.message}`);
}

try {
  const si = require('systeminformation');
  console.log('✓ systeminformation module loaded successfully');
} catch (e) {
  console.log(`✗ Failed to load systeminformation: ${e.message}`);
}

try {
  const Service = require('node-windows').Service;
  console.log('✓ node-windows module loaded successfully');
} catch (e) {
  console.log(`✗ Failed to load node-windows: ${e.message}`);
}

console.log('\n=== TEST COMPLETED ==='); 