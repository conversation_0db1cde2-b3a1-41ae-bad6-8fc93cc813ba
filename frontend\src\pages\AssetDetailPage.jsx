import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { fetchAssetDetail } from '../api';
import { Box, Typography, Paper, Grid, List, ListItem, ListItemText, Button, Alert, Table, TableHead, TableRow, TableCell, TableBody } from '@mui/material';

export default function AssetDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [asset, setAsset] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAssetDetail(id)
      .then(setAsset)
      .catch(e => setError(e.response?.data?.error || 'Failed to load asset'))
      .finally(() => setLoading(false));
  }, [id]);

  if (loading) return <Typography>Loading asset...</Typography>;
  if (error) return <Alert severity="error">{error}</Alert>;
  if (!asset) return null;

  const sys = asset;
  const disks = JSON.parse(sys.disks || '[]');
  const gpus = JSON.parse(sys.gpus || '[]');
  const nics = JSON.parse(sys.network_adapters || '[]');
  const ramSlots = JSON.parse(sys.ram_slots || '[]');
  const macs = JSON.parse(sys.mac_addresses || '[]');
  const ips = JSON.parse(sys.ip_addresses || '[]');

  return (
    <Box p={3}>
      <Button variant="outlined" onClick={() => navigate(-1)} sx={{ mb: 2 }}>Back</Button>
      <Typography variant="h4" gutterBottom>Asset Detail: {sys.hostname}</Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">System Information</Typography>
            <List>
              <ListItem><ListItemText primary="Hostname" secondary={sys.hostname} /></ListItem>
              <ListItem><ListItemText primary="IP Addresses" secondary={ips.join(', ')} /></ListItem>
              <ListItem><ListItemText primary="MAC Addresses" secondary={macs.join(', ')} /></ListItem>
              <ListItem><ListItemText primary="OS" secondary={`${sys.os_name} ${sys.os_version} (${sys.os_arch})`} /></ListItem>
              <ListItem><ListItemText primary="OS Build" secondary={sys.os_build} /></ListItem>
              <ListItem><ListItemText primary="BIOS Serial" secondary={sys.bios_serial} /></ListItem>
              <ListItem><ListItemText primary="Manufacturer" secondary={sys.manufacturer} /></ListItem>
              <ListItem><ListItemText primary="Model" secondary={sys.model} /></ListItem>
              <ListItem><ListItemText primary="CPU" secondary={`${sys.cpu_model} (${sys.cpu_cores} cores, ${sys.cpu_threads} threads, ${sys.cpu_speed} GHz)`} /></ListItem>
              <ListItem><ListItemText primary="RAM Total" secondary={Math.round((sys.ram_total || 0) / (1024*1024*1024)) + ' GB'} /></ListItem>
              <ListItem><ListItemText primary="RAM Slots" secondary={ramSlots.length} /></ListItem>
              <ListItem><ListItemText primary="Location" secondary={sys.location || 'N/A'} /></ListItem>
              <ListItem><ListItemText primary="Last Seen" secondary={new Date(sys.last_seen).toLocaleString()} /></ListItem>
            </List>
          </Paper>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">Disks</Typography>
            <List>
              {disks.map((d, i) => (
                <ListItem key={i}><ListItemText primary={d.model} secondary={`Size: ${Math.round((d.size || 0) / (1024*1024*1024))} GB, Health: ${d.health}`} /></ListItem>
              ))}
            </List>
          </Paper>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">GPUs</Typography>
            <List>
              {gpus.map((g, i) => (
                <ListItem key={i}><ListItemText primary={g.model} secondary={`Driver: ${g.driver}`} /></ListItem>
              ))}
            </List>
          </Paper>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6">Network Adapters</Typography>
            <List>
              {nics.map((n, i) => (
                <ListItem key={i}><ListItemText primary={n.name} secondary={`Status: ${n.status}`} /></ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Installed Software</Typography>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Publisher</TableCell>
                  <TableCell>Install Date</TableCell>
                  <TableCell>Version</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {asset.software && asset.software.length > 0 ? asset.software.map(sw => (
                  <TableRow key={sw.id}>
                    <TableCell>{sw.name}</TableCell>
                    <TableCell>{sw.publisher}</TableCell>
                    <TableCell>{sw.install_date}</TableCell>
                    <TableCell>{sw.version}</TableCell>
                  </TableRow>
                )) : (
                  <TableRow><TableCell colSpan={4}>No software data</TableCell></TableRow>
                )}
              </TableBody>
            </Table>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
} 