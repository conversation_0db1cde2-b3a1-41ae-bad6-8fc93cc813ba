const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const router = express.Router();

function checkJwt(req, res, next) {
  const auth = req.headers['authorization'] || '';
  const token = auth.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'Missing token' });
  try {
    req.user = jwt.verify(token, process.env.JWT_SECRET);
    if (req.user.role !== 'admin') return res.status(403).json({ error: 'Admin only' });
    next();
  } catch {
    return res.status(401).json({ error: 'Invalid token' });
  }
}

// GET /users - list users
router.get('/', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [users] = await db.query('SELECT u.id, u.username, r.name as role, u.created_at FROM users u JOIN roles r ON u.role_id = r.id');
  res.json(users);
});

// POST /users - create user
router.post('/', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const { username, password, role } = req.body;
  if (!username || !password || !role) return res.status(400).json({ error: 'Missing fields' });
  const [roleRows] = await db.query('SELECT id FROM roles WHERE name = ?', [role]);
  if (!roleRows.length) return res.status(400).json({ error: 'Invalid role' });
  const password_hash = await bcrypt.hash(password, 10);
  try {
    await db.query('INSERT INTO users (username, password_hash, role_id) VALUES (?, ?, ?)', [username, password_hash, roleRows[0].id]);
    res.json({ message: 'User created' });
  } catch (e) {
    res.status(400).json({ error: 'Username already exists' });
  }
});

// PUT /users/:id - update user
router.put('/:id', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const { password, role } = req.body;
  const updates = [];
  const params = [];
  if (password) {
    updates.push('password_hash = ?');
    params.push(await bcrypt.hash(password, 10));
  }
  if (role) {
    const [roleRows] = await db.query('SELECT id FROM roles WHERE name = ?', [role]);
    if (!roleRows.length) return res.status(400).json({ error: 'Invalid role' });
    updates.push('role_id = ?');
    params.push(roleRows[0].id);
  }
  if (!updates.length) return res.status(400).json({ error: 'No updates' });
  params.push(req.params.id);
  await db.query(`UPDATE users SET ${updates.join(', ')} WHERE id = ?`, params);
  res.json({ message: 'User updated' });
});

// DELETE /users/:id - delete user
router.delete('/:id', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  await db.query('DELETE FROM users WHERE id = ?', [req.params.id]);
  res.json({ message: 'User deleted' });
});

module.exports = router; 