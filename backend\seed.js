const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');

async function main() {
  const db = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'shashank',
    database: 'windowsagent'
  });

  // Change these as needed
  const username = 'admin';
  const password = 'admin123';
  const role = 'admin';

  // Get role_id for 'admin'
  const [roles] = await db.query('SELECT id FROM roles WHERE name = ?', [role]);
  if (!roles.length) throw new Error('Role not found');
  const role_id = roles[0].id;

  // Hash password
  const password_hash = await bcrypt.hash(password, 10);

  // Insert user
  try {
    await db.query(
      'INSERT INTO users (username, password_hash, role_id) VALUES (?, ?, ?)',
      [username, password_hash, role_id]
    );
    console.log('Admin user created!');
  } catch (e) {
    console.error('Error creating user:', e.message);
  } finally {
    await db.end();
  }
}

main();