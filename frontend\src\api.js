import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

let token = localStorage.getItem('token');

export const api = axios.create({
  baseURL: API_URL,
});

api.interceptors.request.use((config) => {
  if (token) config.headers.Authorization = `Bearer ${token}`;
  return config;
});

export function setToken(newToken) {
  token = newToken;
  if (token) localStorage.setItem('token', token);
  else localStorage.removeItem('token');
}

export function getToken() {
  return token;
}

export async function login(username, password) {
  const res = await api.post('/auth/login', { username, password });
  setToken(res.data.token);
  return res.data;
}

export async function fetchAssets() {
  const res = await api.get('/assets');
  return res.data;
}

export async function fetchAssetDetail(id) {
  const res = await api.get(`/assets/${id}`);
  return res.data;
}

export async function fetchSummary() {
  const res = await api.get('/assets/summary/all');
  return res.data;
}

export default api; 